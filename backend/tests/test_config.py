#!/usr/bin/env python3
"""
Test Configuration for Push Notification System Tests

This module contains configuration settings and constants used across
the notification test suite.
"""

import os
from typing import Dict, List, Any

# Target user configuration
TARGET_USER_ID = "mcaK5709t3MZAcUpdAeEGrmYgaT2"
TARGET_USER_NAME = "Test User"

# Mock data configurations
MOCK_FCM_TOKEN = "test_fcm_token_12345_abcdef"
MOCK_TIMEZONE = "America/New_York"
MOCK_DEVICE_ID = "test_device_12345"

# Test notification content
TEST_NOTIFICATIONS = {
    "morning": {
        "title": "Memorion (TEST)",
        "body": "TEST: Wear your Memorion device to capture your conversations today."
    },
    "summary": {
        "title": "Here is your action plan for tomorrow (TEST)",
        "body": "TEST SUMMARY: Your daily conversation insights are ready."
    },
    "custom": {
        "title": "Custom Test Notification",
        "body": "This is a custom test notification for validation."
    }
}

# Mock user data structure
MOCK_USER_DATA = {
    "fcm_token": MOCK_FCM_TOKEN,
    "time_zone": MOCK_TIMEZONE,
    "device_id": MOCK_DEVICE_ID,
    "created_at": "2024-01-01T00:00:00Z",
    "last_active": "2024-01-01T12:00:00Z"
}

# Test timezones for bypass testing
TEST_TIMEZONES = [
    "UTC",
    "America/New_York",
    "America/Los_Angeles", 
    "Europe/London",
    "Europe/Paris",
    "Asia/Tokyo",
    "Asia/Shanghai",
    "Australia/Sydney"
]

# Firebase error scenarios for testing
FCM_ERROR_SCENARIOS = [
    {
        "error": "requested entity was not found",
        "description": "Token not found (app likely uninstalled)",
        "should_remove_token": True
    },
    {
        "error": "invalid-registration-token",
        "description": "Invalid FCM token format",
        "should_remove_token": True
    },
    {
        "error": "registration-token-not-registered",
        "description": "Token not registered (app uninstalled/token expired)",
        "should_remove_token": True
    },
    {
        "error": "sender-id-mismatch",
        "description": "Token registered with different sender ID",
        "should_remove_token": True
    },
    {
        "error": "authentication",
        "description": "Authentication error with Firebase",
        "should_remove_token": False
    },
    {
        "error": "quota-exceeded",
        "description": "FCM quota exceeded - rate limiting in effect",
        "should_remove_token": False
    },
    {
        "error": "unavailable",
        "description": "FCM service temporarily unavailable",
        "should_remove_token": False
    }
]

# Test conversation data for summary testing
MOCK_CONVERSATIONS = [
    {
        "id": "conv_001",
        "text": "Had a great meeting with the team about the new project.",
        "timestamp": "2024-01-01T09:00:00Z",
        "participants": ["user", "colleague"]
    },
    {
        "id": "conv_002", 
        "text": "Discussed vacation plans with family over dinner.",
        "timestamp": "2024-01-01T19:00:00Z",
        "participants": ["user", "family"]
    }
]

# Mock summary responses
MOCK_SUMMARIES = {
    "with_conversations": "Today you had 2 meaningful conversations: a productive team meeting about the new project and quality family time discussing vacation plans.",
    "no_conversations": "No conversations were captured today. Remember to wear your Memorion device to capture meaningful moments.",
    "error_summary": "Unable to generate summary at this time. Please try again later."
}

# Test environment settings
TEST_ENVIRONMENT = {
    "firebase_project_id": "test-project",
    "firebase_app_name": "test-app",
    "mock_database": True,
    "mock_messaging": True,
    "log_level": "INFO"
}

# Notification message templates
NOTIFICATION_MESSAGE_TEMPLATES = {
    "daily_summary": {
        "from_integration": "false",
        "type": "day_summary", 
        "notification_type": "daily_summary",
        "navigate_to": "/chat/omi"
    },
    "morning_reminder": {
        "from_integration": "false",
        "type": "reminder",
        "notification_type": "daily_reminder", 
        "navigate_to": "/home"
    },
    "plugin_notification": {
        "from_integration": "true",
        "type": "plugin",
        "notification_type": "plugin",
        "navigate_to": "/plugins"
    }
}

# Test batch sizes for bulk operations
BULK_TEST_SIZES = [1, 5, 10, 50, 100, 500]

# Rate limiting test configuration
RATE_LIMIT_CONFIG = {
    "max_notifications_per_hour": 10,
    "test_intervals": [1, 5, 10, 30, 60],  # seconds
    "burst_test_count": 15
}


def get_test_user_data(user_id: str = None) -> Dict[str, Any]:
    """
    Get mock user data for testing.
    
    Args:
        user_id: Optional user ID, defaults to TARGET_USER_ID
        
    Returns:
        Dictionary containing mock user data
    """
    data = MOCK_USER_DATA.copy()
    if user_id:
        data["user_id"] = user_id
    else:
        data["user_id"] = TARGET_USER_ID
    return data


def get_test_notification_data(notification_type: str = "custom") -> Dict[str, Any]:
    """
    Get test notification data.
    
    Args:
        notification_type: Type of notification (morning, summary, custom)
        
    Returns:
        Dictionary containing notification data
    """
    if notification_type in TEST_NOTIFICATIONS:
        return TEST_NOTIFICATIONS[notification_type].copy()
    else:
        return TEST_NOTIFICATIONS["custom"].copy()


def get_mock_conversation_data(count: int = 2) -> List[Dict[str, Any]]:
    """
    Get mock conversation data for testing.
    
    Args:
        count: Number of conversations to return
        
    Returns:
        List of mock conversation dictionaries
    """
    return MOCK_CONVERSATIONS[:count]


def get_fcm_error_scenario(error_type: str) -> Dict[str, Any]:
    """
    Get FCM error scenario for testing.
    
    Args:
        error_type: Type of error to simulate
        
    Returns:
        Dictionary containing error scenario data
    """
    for scenario in FCM_ERROR_SCENARIOS:
        if error_type in scenario["error"]:
            return scenario
    
    # Return generic error if not found
    return {
        "error": error_type,
        "description": f"Generic error: {error_type}",
        "should_remove_token": False
    }


def is_test_environment() -> bool:
    """
    Check if we're running in a test environment.
    
    Returns:
        True if in test environment, False otherwise
    """
    return os.getenv("TESTING", "false").lower() == "true"


def get_test_config() -> Dict[str, Any]:
    """
    Get complete test configuration.
    
    Returns:
        Dictionary containing all test configuration
    """
    return {
        "target_user_id": TARGET_USER_ID,
        "mock_data": MOCK_USER_DATA,
        "test_notifications": TEST_NOTIFICATIONS,
        "test_timezones": TEST_TIMEZONES,
        "error_scenarios": FCM_ERROR_SCENARIOS,
        "environment": TEST_ENVIRONMENT,
        "templates": NOTIFICATION_MESSAGE_TEMPLATES,
        "bulk_sizes": BULK_TEST_SIZES,
        "rate_limits": RATE_LIMIT_CONFIG
    }


# Export commonly used constants
__all__ = [
    "TARGET_USER_ID",
    "MOCK_FCM_TOKEN", 
    "MOCK_TIMEZONE",
    "MOCK_USER_DATA",
    "TEST_NOTIFICATIONS",
    "TEST_TIMEZONES",
    "FCM_ERROR_SCENARIOS",
    "get_test_user_data",
    "get_test_notification_data", 
    "get_mock_conversation_data",
    "get_fcm_error_scenario",
    "is_test_environment",
    "get_test_config"
]
