#!/usr/bin/env python3
"""
Test Runner for Push Notification System Tests

This script provides an easy way to run the comprehensive push notification tests
with various options and configurations.

Usage:
    python3 backend/tests/run_notification_tests.py [options]
    
Options:
    --verbose, -v: Run tests with verbose output
    --specific TEST_NAME: Run a specific test method
    --integration: Run only integration tests
    --unit: Run only unit tests
    --coverage: Run tests with coverage report (requires coverage package)
"""

import argparse
import os
import sys
import unittest
from pathlib import Path

# Add the backend directory to Python path
backend_dir = Path(__file__).parent.parent
sys.path.insert(0, str(backend_dir))

# Color codes for output
class Colors:
    GREEN = '\033[92m'
    RED = '\033[91m'
    YELLOW = '\033[93m'
    BLUE = '\033[94m'
    BOLD = '\033[1m'
    END = '\033[0m'


def print_header():
    """Print test runner header."""
    print(f"{Colors.BLUE}{Colors.BOLD}")
    print("=" * 60)
    print("  PUSH NOTIFICATION SYSTEM - TEST RUNNER")
    print("=" * 60)
    print(f"{Colors.END}")
    print(f"{Colors.YELLOW}Target User ID: mcaK5709t3MZAcUpdAeEGrmYgaT2{Colors.END}")
    print(f"{Colors.YELLOW}Test Features: Timezone bypass, User targeting, Mock services{Colors.END}")
    print()


def run_tests_with_coverage():
    """Run tests with coverage reporting."""
    try:
        import coverage
    except ImportError:
        print(f"{Colors.RED}Coverage package not installed. Install with: pip install coverage{Colors.END}")
        return False
    
    print(f"{Colors.BLUE}Running tests with coverage analysis...{Colors.END}")
    
    # Initialize coverage
    cov = coverage.Coverage(source=['utils.notifications', 'database.notifications', 'models.notification_message'])
    cov.start()
    
    # Run tests
    loader = unittest.TestLoader()
    suite = loader.discover('backend/tests', pattern='test_push_notifications.py')
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # Stop coverage and generate report
    cov.stop()
    cov.save()
    
    print(f"\n{Colors.BLUE}Coverage Report:{Colors.END}")
    cov.report()
    
    # Generate HTML report
    try:
        cov.html_report(directory='backend/tests/coverage_html')
        print(f"{Colors.GREEN}HTML coverage report generated in: backend/tests/coverage_html/{Colors.END}")
    except Exception as e:
        print(f"{Colors.YELLOW}Could not generate HTML report: {e}{Colors.END}")
    
    return result.wasSuccessful()


def run_specific_test(test_name, verbose=False):
    """Run a specific test method."""
    print(f"{Colors.BLUE}Running specific test: {test_name}{Colors.END}")
    
    # Import test module
    from test_push_notifications import TestPushNotificationSystem, TestNotificationIntegration
    
    # Create test suite with specific test
    suite = unittest.TestSuite()
    
    # Try to find the test in both test classes
    test_classes = [TestPushNotificationSystem, TestNotificationIntegration]
    test_found = False
    
    for test_class in test_classes:
        if hasattr(test_class, test_name):
            suite.addTest(test_class(test_name))
            test_found = True
            break
    
    if not test_found:
        print(f"{Colors.RED}Test method '{test_name}' not found{Colors.END}")
        print(f"{Colors.YELLOW}Available tests:{Colors.END}")
        for test_class in test_classes:
            methods = [method for method in dir(test_class) if method.startswith('test_')]
            for method in methods:
                print(f"  - {method}")
        return False
    
    # Run the specific test
    runner = unittest.TextTestRunner(verbosity=2 if verbose else 1)
    result = runner.run(suite)
    
    return result.wasSuccessful()


def run_test_category(category, verbose=False):
    """Run tests by category (unit or integration)."""
    print(f"{Colors.BLUE}Running {category} tests...{Colors.END}")
    
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    if category == 'unit':
        from test_push_notifications import TestPushNotificationSystem
        suite.addTests(loader.loadTestsFromTestCase(TestPushNotificationSystem))
    elif category == 'integration':
        from test_push_notifications import TestNotificationIntegration
        suite.addTests(loader.loadTestsFromTestCase(TestNotificationIntegration))
    else:
        print(f"{Colors.RED}Unknown test category: {category}{Colors.END}")
        return False
    
    runner = unittest.TextTestRunner(verbosity=2 if verbose else 1)
    result = runner.run(suite)
    
    return result.wasSuccessful()


def run_all_tests(verbose=False):
    """Run all notification tests."""
    print(f"{Colors.BLUE}Running all push notification tests...{Colors.END}")
    
    loader = unittest.TestLoader()
    suite = loader.discover('backend/tests', pattern='test_push_notifications.py')
    runner = unittest.TextTestRunner(verbosity=2 if verbose else 1)
    result = runner.run(suite)
    
    return result.wasSuccessful()


def validate_environment():
    """Validate that the test environment is properly set up."""
    print(f"{Colors.BLUE}Validating test environment...{Colors.END}")

    # Set up minimal environment variables for testing
    test_env_vars = {
        'ENCRYPTION_SECRET': 'test_encryption_key_32_bytes_long_12345',
        'FIREBASE_PROJECT_ID': 'test-project',
        'TESTING': 'true',
        'OPENAI_API_KEY': 'test-openai-key-for-testing',
        'ANTHROPIC_API_KEY': 'test-anthropic-key-for-testing'
    }

    for key, value in test_env_vars.items():
        if not os.getenv(key):
            os.environ[key] = value
            print(f"{Colors.YELLOW}Set test environment variable: {key}{Colors.END}")

    # Check if required modules can be imported
    required_modules = [
        'utils.notifications',
        'database.notifications',
        'models.notification_message'
    ]

    # Optional modules that might have dependencies
    optional_modules = [
        'utils.other.notifications'
    ]

    missing_modules = []
    for module in required_modules:
        try:
            __import__(module)
            print(f"{Colors.GREEN}✓ {module}{Colors.END}")
        except ImportError as e:
            print(f"{Colors.RED}✗ {module}: {e}{Colors.END}")
            missing_modules.append(module)

    # Try optional modules but don't fail if they can't be imported
    for module in optional_modules:
        try:
            __import__(module)
            print(f"{Colors.GREEN}✓ {module}{Colors.END}")
        except ImportError as e:
            print(f"{Colors.YELLOW}⚠ {module}: {e} (optional){Colors.END}")

    if missing_modules:
        print(f"\n{Colors.RED}Missing required modules. Please ensure you're running from the correct directory.{Colors.END}")
        return False

    print(f"\n{Colors.GREEN}Environment validation successful!{Colors.END}")
    return True


def main():
    parser = argparse.ArgumentParser(description='Run push notification system tests')
    parser.add_argument('--verbose', '-v', action='store_true', help='Run tests with verbose output')
    parser.add_argument('--specific', type=str, help='Run a specific test method')
    parser.add_argument('--integration', action='store_true', help='Run only integration tests')
    parser.add_argument('--unit', action='store_true', help='Run only unit tests')
    parser.add_argument('--coverage', action='store_true', help='Run tests with coverage report')
    parser.add_argument('--validate', action='store_true', help='Only validate environment setup')
    
    args = parser.parse_args()
    
    print_header()
    
    # Validate environment first
    if not validate_environment():
        sys.exit(1)
    
    if args.validate:
        print(f"{Colors.GREEN}Environment validation complete. Ready to run tests!{Colors.END}")
        return
    
    success = True
    
    try:
        if args.coverage:
            success = run_tests_with_coverage()
        elif args.specific:
            success = run_specific_test(args.specific, args.verbose)
        elif args.integration:
            success = run_test_category('integration', args.verbose)
        elif args.unit:
            success = run_test_category('unit', args.verbose)
        else:
            success = run_all_tests(args.verbose)
        
        # Print summary
        print("\n" + "=" * 60)
        if success:
            print(f"{Colors.GREEN}{Colors.BOLD}✓ ALL TESTS PASSED{Colors.END}")
            print(f"{Colors.GREEN}Push notification system is working correctly!{Colors.END}")
        else:
            print(f"{Colors.RED}{Colors.BOLD}✗ SOME TESTS FAILED{Colors.END}")
            print(f"{Colors.RED}Please review the test output above for details.{Colors.END}")
        print("=" * 60)
        
    except KeyboardInterrupt:
        print(f"\n{Colors.YELLOW}Tests interrupted by user{Colors.END}")
        success = False
    except Exception as e:
        print(f"\n{Colors.RED}Unexpected error running tests: {e}{Colors.END}")
        import traceback
        traceback.print_exc()
        success = False
    
    sys.exit(0 if success else 1)


if __name__ == '__main__':
    main()
