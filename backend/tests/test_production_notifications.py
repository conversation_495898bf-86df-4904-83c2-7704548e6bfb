#!/usr/bin/env python3
"""
Production Notification Test - Real Functions, Real Data, Timezone Bypass

This script tests the actual production notification functions from 
backend/utils/other/notifications.py with real data and content, bypassing 
timezone restrictions to target only user ID: mcaK5709t3MZAcUpdAeEGrmYgaT2

Features:
- Uses actual send_daily_notification() and send_daily_summary_notification()
- Bypasses timezone filtering to send immediately
- Uses real conversation data if available
- Targets only the specified user ID
- Sends actual production-style notifications

Usage:
    python3 backend/tests/test_production_notifications.py [--dry-run]
"""

import asyncio
import json
import os
import sys
import argparse
import logging
from datetime import datetime, timezone, time
from pathlib import Path
from unittest.mock import patch, Mock

# Add the backend directory to Python path
backend_dir = Path(__file__).parent.parent
sys.path.insert(0, str(backend_dir))

# Load environment variables from .env file
try:
    from dotenv import load_dotenv
    env_path = backend_dir / '.env'
    if env_path.exists():
        load_dotenv(env_path)
        print(f"✓ Loaded environment variables from {env_path}")
    else:
        print(f"⚠️  .env file not found at {env_path}")
except ImportError:
    print("ℹ python-dotenv not available, using system environment variables")

# Set up environment variables (no test overrides)
os.environ.setdefault('TESTING', 'false')  # Use real services

# Initialize Firebase first
import firebase_admin

def initialize_firebase():
    """Initialize Firebase Admin SDK."""
    try:
        # Check if Firebase is already initialized
        firebase_admin.get_app()
        print("✓ Firebase already initialized")
        return True
    except ValueError:
        pass
    
    try:
        if os.environ.get('SERVICE_ACCOUNT_JSON'):
            service_account_info = json.loads(os.environ["SERVICE_ACCOUNT_JSON"])
            credentials = firebase_admin.credentials.Certificate(service_account_info)
            firebase_admin.initialize_app(credentials)
            print("✓ Firebase initialized with SERVICE_ACCOUNT_JSON")
            return True
        else:
            firebase_admin.initialize_app()
            print("✓ Firebase initialized with default credentials")
            return True
    except Exception as e:
        print(f"❌ Failed to initialize Firebase: {e}")
        return False

# Initialize Firebase before importing notification components
if not initialize_firebase():
    print("❌ Cannot proceed without Firebase initialization")
    sys.exit(1)

# Import production notification functions
from utils.other.notifications import (
    send_daily_notification,
    send_daily_summary_notification,
    _send_summary_notification,
    _get_timezones_at_time,
    _get_users_in_timezone,
    _send_notification_for_time
)
from database.notifications import get_token, get_users_id_in_timezones, get_users_token_in_timezones
from database.conversations import filter_conversations_by_date
from utils.llm.external_integrations import get_conversation_summary

# Color codes for output
class Colors:
    GREEN = '\033[92m'
    RED = '\033[91m'
    YELLOW = '\033[93m'
    BLUE = '\033[94m'
    BOLD = '\033[1m'
    END = '\033[0m'


class ProductionNotificationTester:
    """Test production notification functions with real data and timezone bypass."""
    
    TARGET_USER_ID = "mcaK5709t3MZAcUpdAeEGrmYgaT2"
    
    def __init__(self, dry_run=False):
        self.dry_run = dry_run
        self.target_user_id = self.TARGET_USER_ID
        self.logger = self.setup_logging()
        
    def setup_logging(self):
        """Setup logging configuration."""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(sys.stdout),
                logging.FileHandler('/tmp/production_notification_test.log', mode='a')
            ]
        )
        return logging.getLogger('ProductionNotificationTester')
    
    def print_header(self):
        """Print test header."""
        print(f"{Colors.BLUE}{Colors.BOLD}")
        print("=" * 80)
        print("  PRODUCTION NOTIFICATION TEST - REAL FUNCTIONS & DATA")
        print("=" * 80)
        print(f"{Colors.END}")
        print(f"{Colors.YELLOW}Target User ID: {self.target_user_id}{Colors.END}")
        print(f"{Colors.YELLOW}Dry Run Mode: {'ON' if self.dry_run else 'OFF'}{Colors.END}")
        print(f"{Colors.YELLOW}Testing: send_daily_notification() & send_daily_summary_notification(){Colors.END}")
        if not self.dry_run:
            print(f"{Colors.RED}⚠️  REAL PRODUCTION NOTIFICATIONS WILL BE SENT!{Colors.END}")
        print()
    
    def get_user_info(self):
        """Get user token and timezone information."""
        try:
            self.logger.info(f"Retrieving user info for: {self.target_user_id}")
            result = get_token(self.target_user_id)
            
            if result:
                token, user_timezone = result
                self.logger.info(f"Found user - Token: {token[:20]}..., Timezone: {user_timezone}")
                return token, user_timezone
            else:
                self.logger.error(f"No token found for user: {self.target_user_id}")
                return None, None
                
        except Exception as e:
            self.logger.error(f"Error retrieving user info: {e}")
            return None, None
    
    def check_user_conversations(self):
        """Check if user has conversations for summary generation."""
        try:
            self.logger.info("Checking for user conversations...")
            today_start = datetime.combine(datetime.now().date(), time.min)
            today_end = datetime.now()
            
            conversations = filter_conversations_by_date(self.target_user_id, today_start, today_end)
            
            if conversations:
                self.logger.info(f"Found {len(conversations)} conversations for today")
                return conversations
            else:
                self.logger.info("No conversations found for today")
                return []
                
        except Exception as e:
            self.logger.error(f"Error checking conversations: {e}")
            return []
    
    async def test_morning_notification_with_bypass(self):
        """Test send_daily_notification() with timezone bypass."""
        print(f"{Colors.BOLD}🌅 Testing Morning Notification (send_daily_notification){Colors.END}")
        print("-" * 60)
        
        if self.dry_run:
            print(f"{Colors.YELLOW}DRY RUN: Would test morning notification with timezone bypass{Colors.END}")
            return True
        
        try:
            # Patch the timezone function to return user's timezone
            with patch('utils.other.notifications._get_timezones_at_time') as mock_timezones:
                # Force timezone bypass by returning user's timezone
                mock_timezones.return_value = ["Asia/Shanghai"]  # User's actual timezone
                
                # Patch user retrieval to return only our target user
                with patch('database.notifications.get_users_token_in_timezones') as mock_get_users:
                    user_token, _ = self.get_user_info()
                    if not user_token:
                        print(f"{Colors.RED}❌ Cannot get user token{Colors.END}")
                        return False
                    
                    mock_get_users.return_value = [user_token]
                    
                    self.logger.info("Sending production morning notification with timezone bypass...")
                    print(f"{Colors.BLUE}📤 Sending: 'Memorion - Wear your device to capture conversations'{Colors.END}")
                    
                    result = await send_daily_notification()
                    
                    if result:
                        print(f"{Colors.GREEN}✅ Morning notification sent successfully!{Colors.END}")
                        print(f"   Users notified: {len(result)}")
                        print(f"   Check your iOS app for the morning reminder")
                        return True
                    else:
                        print(f"{Colors.RED}❌ Morning notification failed{Colors.END}")
                        return False
                        
        except Exception as e:
            self.logger.error(f"Error in morning notification test: {e}")
            print(f"{Colors.RED}❌ Morning notification test failed: {e}{Colors.END}")
            return False
    
    async def test_summary_notification_with_bypass(self):
        """Test send_daily_summary_notification() with timezone bypass."""
        print(f"{Colors.BOLD}🌙 Testing Evening Summary Notification (send_daily_summary_notification){Colors.END}")
        print("-" * 60)
        
        # Check for conversations first
        conversations = self.check_user_conversations()
        
        if self.dry_run:
            print(f"{Colors.YELLOW}DRY RUN: Would test summary notification{Colors.END}")
            print(f"   Conversations found: {len(conversations)}")
            if conversations:
                print(f"   Would generate summary from real conversation data")
            else:
                print(f"   Would handle no-conversations scenario")
            return True
        
        try:
            # Patch the timezone function to return user's timezone
            with patch('utils.other.notifications._get_timezones_at_time') as mock_timezones:
                mock_timezones.return_value = ["Asia/Shanghai"]  # User's actual timezone
                
                # Patch user retrieval to return only our target user
                with patch('database.notifications.get_users_id_in_timezones') as mock_get_users:
                    user_token, _ = self.get_user_info()
                    if not user_token:
                        print(f"{Colors.RED}❌ Cannot get user token{Colors.END}")
                        return False
                    
                    # Return user data in the format expected by summary function
                    mock_get_users.return_value = [(self.target_user_id, user_token)]
                    
                    if conversations:
                        self.logger.info(f"Sending summary notification with {len(conversations)} conversations...")
                        print(f"{Colors.BLUE}📤 Generating summary from {len(conversations)} real conversations{Colors.END}")
                    else:
                        self.logger.info("Sending summary notification with no conversations...")
                        print(f"{Colors.BLUE}📤 Testing no-conversations scenario{Colors.END}")
                    
                    result = await send_daily_summary_notification()
                    
                    if conversations:
                        print(f"{Colors.GREEN}✅ Summary notification sent successfully!{Colors.END}")
                        print(f"   Generated from real conversation data")
                        print(f"   Check your iOS app for: 'Here is your action plan for tomorrow'")
                        return True
                    else:
                        print(f"{Colors.YELLOW}ℹ️  No conversations found - notification not sent (expected behavior){Colors.END}")
                        print(f"   This is the correct behavior when no conversations exist")
                        return True
                        
        except Exception as e:
            self.logger.error(f"Error in summary notification test: {e}")
            print(f"{Colors.RED}❌ Summary notification test failed: {e}{Colors.END}")
            import traceback
            traceback.print_exc()
            return False
    
    async def test_direct_summary_with_conversations(self):
        """Test direct summary notification if conversations exist."""
        print(f"{Colors.BOLD}📝 Testing Direct Summary Generation{Colors.END}")
        print("-" * 60)
        
        conversations = self.check_user_conversations()
        
        if not conversations:
            print(f"{Colors.YELLOW}ℹ️  No conversations found - skipping direct summary test{Colors.END}")
            return True
        
        if self.dry_run:
            print(f"{Colors.YELLOW}DRY RUN: Would generate summary from {len(conversations)} conversations{Colors.END}")
            return True
        
        try:
            user_token, _ = self.get_user_info()
            if not user_token:
                print(f"{Colors.RED}❌ Cannot get user token{Colors.END}")
                return False
            
            self.logger.info("Testing direct summary generation...")
            print(f"{Colors.BLUE}📤 Generating summary directly from conversation data{Colors.END}")
            
            # Call the direct summary function
            user_data = (self.target_user_id, user_token)
            _send_summary_notification(user_data)
            
            print(f"{Colors.GREEN}✅ Direct summary notification sent!{Colors.END}")
            print(f"   Used real conversation data from today")
            print(f"   Check your iOS app for the personalized summary")
            return True
            
        except Exception as e:
            self.logger.error(f"Error in direct summary test: {e}")
            print(f"{Colors.RED}❌ Direct summary test failed: {e}{Colors.END}")
            return False
    
    async def run_all_tests(self):
        """Run all production notification tests."""
        self.print_header()
        
        # Verify user exists
        user_token, user_timezone = self.get_user_info()
        if not user_token:
            print(f"{Colors.RED}❌ Cannot proceed: User {self.target_user_id} not found{Colors.END}")
            return False
        
        print(f"{Colors.GREEN}✓ Found target user{Colors.END}")
        print(f"  Token: {user_token[:20]}...")
        print(f"  Timezone: {user_timezone}")
        print()
        
        # Run tests
        tests = [
            ("Morning Notification", self.test_morning_notification_with_bypass),
            ("Summary Notification", self.test_summary_notification_with_bypass),
            ("Direct Summary", self.test_direct_summary_with_conversations)
        ]
        
        results = []
        
        for i, (test_name, test_func) in enumerate(tests, 1):
            print(f"{Colors.BOLD}Test {i}/3: {test_name}{Colors.END}")
            
            try:
                result = await test_func()
                results.append((test_name, result))
                
                if result:
                    print(f"{Colors.GREEN}✓ {test_name} completed successfully{Colors.END}")
                else:
                    print(f"{Colors.RED}✗ {test_name} failed{Colors.END}")
                
                # Wait between tests
                if not self.dry_run and i < len(tests):
                    print(f"{Colors.YELLOW}⏳ Waiting 5 seconds before next test...{Colors.END}")
                    await asyncio.sleep(5)
                
            except Exception as e:
                print(f"{Colors.RED}✗ {test_name} failed with error: {e}{Colors.END}")
                results.append((test_name, False))
            
            print()
        
        # Print summary
        print("=" * 80)
        print(f"{Colors.BOLD}PRODUCTION TEST SUMMARY{Colors.END}")
        print("=" * 80)
        
        passed = sum(1 for _, result in results if result)
        total = len(results)
        
        for test_name, result in results:
            status = "✓ PASS" if result else "✗ FAIL"
            color = Colors.GREEN if result else Colors.RED
            print(f"{color}{status}{Colors.END} {test_name}")
        
        print(f"\nResults: {passed}/{total} tests passed")
        
        if passed == total:
            print(f"\n{Colors.GREEN}{Colors.BOLD}🎉 ALL PRODUCTION TESTS PASSED!{Colors.END}")
            print(f"{Colors.GREEN}You should have received production-style notifications on your iOS app.{Colors.END}")
        else:
            print(f"\n{Colors.YELLOW}⚠️  Some tests failed. Check the logs above.{Colors.END}")
        
        print(f"\n{Colors.BLUE}What was tested:{Colors.END}")
        print("✓ Real send_daily_notification() function")
        print("✓ Real send_daily_summary_notification() function") 
        print("✓ Timezone bypass mechanism")
        print("✓ Real conversation data (if available)")
        print("✓ Production notification content and formatting")
        print("✓ Exclusive targeting of your user ID")
        
        return passed == total


async def main():
    parser = argparse.ArgumentParser(description='Test production notification functions with real data')
    parser.add_argument('--dry-run', action='store_true', 
                       help='Show what would be sent without actually sending notifications')
    
    args = parser.parse_args()
    
    if not args.dry_run:
        print(f"{Colors.RED}⚠️  WARNING: This will send REAL production notifications!{Colors.END}")
        response = input("Continue? (yes/no): ")
        if response.lower() != 'yes':
            print("Test cancelled.")
            return
    
    tester = ProductionNotificationTester(dry_run=args.dry_run)
    success = await tester.run_all_tests()
    
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    asyncio.run(main())
