# Push Notification System - Test Suite

This directory contains comprehensive tests for the push notification system with timezone bypass functionality and targeted user testing.

## Overview

The test suite provides:
- **Timezone Bypass**: Tests can bypass normal timezone filtering to send notifications immediately
- **User Targeting**: All tests target the specific user ID: `mcaK5709t3MZAcUpdAeEGrmYgaT2`
- **Mock Services**: External services (FCM, APNs, Firestore) are mocked for safe testing
- **Error Handling**: Comprehensive testing of error scenarios and edge cases
- **Integration Testing**: End-to-end testing of the complete notification pipeline

## Test Files

### `test_push_notifications.py`
Main test file containing:
- `TestPushNotificationSystem`: Unit tests for individual components
- `TestNotificationIntegration`: Integration tests for complete workflows

### `run_notification_tests.py`
Test runner script with various execution options and reporting features.

## Quick Start

### 1. Basic Test Execution
```bash
# Run all tests
python3 backend/tests/run_notification_tests.py

# Run with verbose output
python3 backend/tests/run_notification_tests.py --verbose

# Validate environment setup
python3 backend/tests/run_notification_tests.py --validate
```

### 2. Targeted Test Execution
```bash
# Run only unit tests
python3 backend/tests/run_notification_tests.py --unit

# Run only integration tests
python3 backend/tests/run_notification_tests.py --integration

# Run a specific test method
python3 backend/tests/run_notification_tests.py --specific test_send_notification_success
```

### 3. Coverage Analysis
```bash
# Install coverage package (if not already installed)
pip install coverage

# Run tests with coverage report
python3 backend/tests/run_notification_tests.py --coverage
```

## Test Categories

### Unit Tests (`TestPushNotificationSystem`)

#### Notification Message Tests
- `test_notification_message_creation`: Tests NotificationMessage model
- `test_send_notification_success`: Tests successful notification sending
- `test_send_notification_invalid_token`: Tests error handling for invalid tokens
- `test_send_bulk_notification`: Tests bulk notification functionality

#### Database Operation Tests
- `test_get_token_only_success`: Tests FCM token retrieval
- `test_get_token_only_user_not_found`: Tests handling of non-existent users
- `test_get_token_with_timezone`: Tests token and timezone retrieval
- `test_get_users_token_in_timezones_targeted_user`: Tests user filtering by timezone

#### Timezone Bypass Tests
- `test_bypass_timezone_filtering`: Tests timezone bypass mechanism
- `test_timezone_bypass_mechanism`: Tests timezone override functionality

#### Notification Flow Tests
- `test_send_daily_notification_to_target_user`: Tests morning notifications
- `test_send_summary_notification_with_conversations`: Tests evening summaries
- `test_send_summary_notification_no_conversations`: Tests no-conversation handling

#### Error Handling Tests
- `test_notification_error_handling_scenarios`: Tests various FCM error scenarios

### Integration Tests (`TestNotificationIntegration`)

#### End-to-End Tests
- `test_end_to_end_notification_flow`: Tests complete notification pipeline

## Target User Configuration

All tests are configured to target the user with ID: `mcaK5709t3MZAcUpdAeEGrmYgaT2`

This is configured in the test classes as:
```python
TARGET_USER_ID = "mcaK5709t3MZAcUpdAeEGrmYgaT2"
```

## Mock Configuration

### Firebase Messaging
- All Firebase messaging calls are mocked to prevent actual notifications
- Mock responses simulate successful and failed notification scenarios
- Token validation and error handling are tested with mock exceptions

### Firestore Database
- Database operations are mocked to simulate user data retrieval
- Mock user data includes FCM tokens and timezone information
- Database errors and edge cases are simulated for testing

### External Services
- FCM (Firebase Cloud Messaging) is fully mocked
- APNs (Apple Push Notification Service) interactions are mocked
- No real external API calls are made during testing

## Timezone Bypass Mechanism

The tests implement timezone bypass in several ways:

### 1. Mock Timezone Functions
```python
@patch('utils.other.notifications._get_timezones_at_time')
def test_bypass_timezone_filtering(self, mock_get_timezones):
    # Return all timezones to bypass filtering
    mock_get_timezones.return_value = ["UTC", "America/New_York", "Europe/London"]
```

### 2. Direct User Targeting
```python
# Mock user retrieval to return specific target user
mock_get_users.return_value = [self.mock_fcm_token]
```

### 3. Override Time-Based Logic
Tests can override the normal time-based notification scheduling to send notifications immediately.

## Error Scenarios Tested

### FCM Error Handling
- Invalid registration tokens
- Expired tokens
- Authentication errors
- Quota exceeded errors
- Service unavailable errors
- Sender ID mismatches

### Database Error Handling
- User not found scenarios
- Missing FCM tokens
- Database connection issues
- Invalid user data

### Network Error Handling
- Connection timeouts
- Service unavailable responses
- Rate limiting scenarios

## Running Tests in Different Environments

### Development Environment
```bash
# Standard test run
python3 backend/tests/run_notification_tests.py --verbose
```

### CI/CD Environment
```bash
# Run with coverage and generate reports
python3 backend/tests/run_notification_tests.py --coverage
```

### Production Validation
```bash
# Validate environment without running tests
python3 backend/tests/run_notification_tests.py --validate
```

## Test Output Examples

### Successful Test Run
```
=============================================================
  PUSH NOTIFICATION SYSTEM - TEST RUNNER
=============================================================
Target User ID: mcaK5709t3MZAcUpdAeEGrmYgaT2
Test Features: Timezone bypass, User targeting, Mock services

Validating test environment...
✓ utils.notifications
✓ database.notifications
✓ models.notification_message
✓ utils.other.notifications

Environment validation successful!

Running all push notification tests...
test_notification_message_creation ... ok
test_send_notification_success ... ok
test_bypass_timezone_filtering ... ok
...

============================================================
✓ ALL TESTS PASSED
Push notification system is working correctly!
============================================================
```

### Failed Test Run
```
============================================================
✗ SOME TESTS FAILED
Please review the test output above for details.
============================================================
```

## Troubleshooting

### Common Issues

#### Import Errors
- Ensure you're running from the correct directory (`/home/<USER>/omi`)
- Check that all required dependencies are installed
- Verify Python path includes the backend directory

#### Mock Failures
- Ensure all external dependencies are properly mocked
- Check that mock configurations match the actual service interfaces
- Verify mock return values match expected data structures

#### Environment Issues
- Run `--validate` flag to check environment setup
- Ensure Firebase credentials are not required for mocked tests
- Check that test database configurations don't conflict with production

### Getting Help

1. Run environment validation: `python3 backend/tests/run_notification_tests.py --validate`
2. Check test output for specific error messages
3. Review mock configurations in the test files
4. Ensure all dependencies are properly installed

## Contributing

When adding new tests:
1. Follow the existing naming convention (`test_*`)
2. Add appropriate mocks for external services
3. Include both success and failure scenarios
4. Update this README with new test descriptions
5. Ensure tests target the specified user ID
